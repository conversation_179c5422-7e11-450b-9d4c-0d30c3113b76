# 📧 EmailJS Setup Guide for Your Portfolio

## 🎯 **What You Need**
You mentioned you have:
- ✅ EmailJS User ID
- ✅ EmailJS Service ID

You still need:
- 📝 EmailJS Template ID

## 🚀 **Complete Setup Steps**

### **Step 1: Get Your Template ID**

1. **Go to EmailJS Dashboard**: https://dashboard.emailjs.com/admin/templates
2. **Click "Create New Template"**
3. **Choose your email service** (Gmail, Outlook, etc.)
4. **Create template with this content**:

**Template Name**: `portfolio_contact`

**Subject**: `New Contact from Portfolio: {{subject}}`

**Content**:
```
Hello Vivekrajiv,

You have received a new message from your portfolio website:

Name: {{from_name}}
Email: {{from_email}}
Subject: {{subject}}

Message:
{{message}}

---
This message was sent from your portfolio contact form.
Reply directly to this email to respond to {{from_name}}.
```

5. **Save the template** and copy the **Template ID**

### **Step 2: Update Your Configuration**

1. **Open**: `scripts/emailjs-config.js`
2. **Replace the placeholder values**:

```javascript
const EMAILJS_CONFIG = {
    USER_ID: 'YOUR_ACTUAL_USER_ID',      // Replace with your User ID
    SERVICE_ID: 'YOUR_ACTUAL_SERVICE_ID', // Replace with your Service ID  
    TEMPLATE_ID: 'YOUR_ACTUAL_TEMPLATE_ID' // Replace with your Template ID
};
```

### **Step 3: Test Your Setup**

1. **Save all files**
2. **Open your portfolio** in browser
3. **Go to Contact section**
4. **Fill out the form** with test data
5. **Click "Send Message"**
6. **Check your email** for the message

## 📋 **Template Variables Reference**

Your EmailJS template can use these variables:
- `{{from_name}}` - Sender's name
- `{{from_email}}` - Sender's email
- `{{subject}}` - Message subject
- `{{message}}` - Message content
- `{{to_email}}` - Your email (<EMAIL>)
- `{{reply_to}}` - Reply-to address (sender's email)

## 🔧 **Troubleshooting**

### **If emails aren't sending:**

1. **Check Console**: Open browser developer tools (F12) and look for errors
2. **Verify IDs**: Make sure all IDs are correct in `emailjs-config.js`
3. **Check EmailJS Dashboard**: Verify your service is active
4. **Test Template**: Send a test email from EmailJS dashboard

### **Common Issues:**

- **"EmailJS not configured"**: Update the IDs in `emailjs-config.js`
- **"Service not found"**: Check your Service ID
- **"Template not found"**: Check your Template ID
- **"User not found"**: Check your User ID (Public Key)

## 📞 **Fallback System**

If EmailJS fails, the form will automatically:
1. **Show error message**
2. **Open email client** with pre-filled content
3. **User can send manually**

This ensures your contact form always works!

## 🎉 **After Setup**

Once configured, your contact form will:
- ✅ **Send emails directly** to <EMAIL>
- ✅ **Show success message** to users
- ✅ **Reset the form** automatically
- ✅ **Include all form data** in the email
- ✅ **Allow direct replies** to the sender

## 📝 **Quick Setup Checklist**

- [ ] Create EmailJS template
- [ ] Copy Template ID
- [ ] Update `scripts/emailjs-config.js` with all three IDs
- [ ] Test the contact form
- [ ] Verify email delivery

## 🔗 **Useful Links**

- **EmailJS Dashboard**: https://dashboard.emailjs.com/admin
- **EmailJS Documentation**: https://www.emailjs.com/docs/
- **Template Editor**: https://dashboard.emailjs.com/admin/templates

---

**Need Help?** 
If you encounter any issues, check the browser console (F12) for error messages and verify all IDs are correctly entered in the configuration file.
