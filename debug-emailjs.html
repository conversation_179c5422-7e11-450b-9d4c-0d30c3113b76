<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>EmailJS Debug Tool</title>
    <script src="https://cdn.jsdelivr.net/npm/@emailjs/browser@3/dist/email.min.js"></script>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input, textarea {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 14px;
        }
        button {
            background-color: #007bff;
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px 5px;
        }
        button:hover {
            background-color: #0056b3;
        }
        .test-btn {
            background-color: #28a745;
        }
        .test-btn:hover {
            background-color: #218838;
        }
        .result {
            margin-top: 20px;
            padding: 15px;
            border-radius: 5px;
            font-family: monospace;
            white-space: pre-wrap;
        }
        .success {
            background-color: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        .error {
            background-color: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
        .info {
            background-color: #d1ecf1;
            border: 1px solid #bee5eb;
            color: #0c5460;
        }
        .step {
            background-color: #f8f9fa;
            padding: 15px;
            margin: 10px 0;
            border-left: 4px solid #007bff;
            border-radius: 5px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 EmailJS Debug Tool</h1>
        
        <div class="step">
            <h3>Step 1: Find Your User ID (Public Key)</h3>
            <p><strong>Go to:</strong> <a href="https://dashboard.emailjs.com/admin/account" target="_blank">EmailJS Account Settings</a></p>
            <p><strong>Look for:</strong> "Public Key" section</p>
            <p><strong>Copy:</strong> The key that looks like "user_xxxxxxxxxxxxxxxxx"</p>
        </div>

        <div class="step">
            <h3>Step 2: Enter Your EmailJS Credentials</h3>
            <form id="configForm">
                <div class="form-group">
                    <label for="userId">User ID (Public Key):</label>
                    <input type="text" id="userId" placeholder="user_xxxxxxxxxxxxxxxxx" required>
                </div>
                
                <div class="form-group">
                    <label for="serviceId">Service ID:</label>
                    <input type="text" id="serviceId" placeholder="service_xxxxxxxxx" required>
                </div>
                
                <div class="form-group">
                    <label for="templateId">Template ID:</label>
                    <input type="text" id="templateId" placeholder="template_xxxxxxxxx" required>
                </div>
                
                <button type="button" onclick="testConnection()">Test Connection</button>
                <button type="button" class="test-btn" onclick="sendTestEmail()">Send Test Email</button>
            </form>
        </div>

        <div class="step">
            <h3>Step 3: Test Email Form</h3>
            <form id="testForm">
                <div class="form-group">
                    <label for="testName">Your Name:</label>
                    <input type="text" id="testName" value="Test User" required>
                </div>
                
                <div class="form-group">
                    <label for="testEmail">Your Email:</label>
                    <input type="email" id="testEmail" value="<EMAIL>" required>
                </div>
                
                <div class="form-group">
                    <label for="testSubject">Subject:</label>
                    <input type="text" id="testSubject" value="Test Message from Portfolio" required>
                </div>
                
                <div class="form-group">
                    <label for="testMessage">Message:</label>
                    <textarea id="testMessage" rows="4" required>This is a test message to verify EmailJS is working correctly.</textarea>
                </div>
            </form>
        </div>

        <div id="result"></div>
    </div>

    <script>
        let currentConfig = {};

        function showResult(message, type) {
            const resultDiv = document.getElementById('result');
            resultDiv.className = `result ${type}`;
            resultDiv.textContent = message;
        }

        function testConnection() {
            const userId = document.getElementById('userId').value.trim();
            const serviceId = document.getElementById('serviceId').value.trim();
            const templateId = document.getElementById('templateId').value.trim();

            if (!userId || !serviceId || !templateId) {
                showResult('Please fill in all fields', 'error');
                return;
            }

            currentConfig = { userId, serviceId, templateId };

            try {
                // Initialize EmailJS
                emailjs.init(userId);
                showResult(`✅ Connection Test Successful!

Configuration:
- User ID: ${userId}
- Service ID: ${serviceId}  
- Template ID: ${templateId}

EmailJS library loaded and initialized successfully.
Now click "Send Test Email" to test actual email sending.`, 'success');
            } catch (error) {
                showResult(`❌ Connection Test Failed!

Error: ${error.message}

Please check:
1. User ID is correct (should start with "user_")
2. EmailJS library is loaded
3. No typos in the IDs`, 'error');
            }
        }

        function sendTestEmail() {
            if (!currentConfig.userId) {
                showResult('Please test connection first', 'error');
                return;
            }

            const testData = {
                from_name: document.getElementById('testName').value,
                from_email: document.getElementById('testEmail').value,
                subject: document.getElementById('testSubject').value,
                message: document.getElementById('testMessage').value,
                to_email: '<EMAIL>',
                reply_to: document.getElementById('testEmail').value
            };

            showResult('🚀 Sending test email...', 'info');

            emailjs.send(currentConfig.serviceId, currentConfig.templateId, testData)
                .then((response) => {
                    showResult(`🎉 SUCCESS! Test email sent successfully!

Response Status: ${response.status}
Response Text: ${response.text}

Check your email (<EMAIL>) for the test message.
If you don't see it, check your spam folder.

Your EmailJS configuration is working correctly!`, 'success');
                })
                .catch((error) => {
                    showResult(`❌ FAILED! Could not send test email.

Error: ${error.text || error.message}

Common issues:
1. Service ID incorrect or service not active
2. Template ID incorrect or template not published
3. Template variables don't match
4. Email service not properly connected

Please check your EmailJS dashboard:
- Services: https://dashboard.emailjs.com/admin
- Templates: https://dashboard.emailjs.com/admin/templates`, 'error');
                });
        }

        // Auto-load config if available
        window.addEventListener('load', () => {
            if (window.EMAILJS_CONFIG) {
                document.getElementById('userId').value = window.EMAILJS_CONFIG.USER_ID || '';
                document.getElementById('serviceId').value = window.EMAILJS_CONFIG.SERVICE_ID || '';
                document.getElementById('templateId').value = window.EMAILJS_CONFIG.TEMPLATE_ID || '';
            }
        });
    </script>
</body>
</html>
