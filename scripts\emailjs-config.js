// EmailJS Configuration
// Replace these values with your actual EmailJS credentials

const EMAILJS_CONFIG = {
  // Your EmailJS User ID (Public Key)
  // Get this from: https://dashboard.emailjs.com/admin/account
  USER_ID: "8vr8bQSmwegoTuNoV",

  // Your EmailJS Service ID
  // Get this from: https://dashboard.emailjs.com/admin
  SERVICE_ID: "service_1ngjkdk",

  // Your EmailJS Template ID
  // Get this from: https://dashboard.emailjs.com/admin/templates
  TEMPLATE_ID: "template_17r2l57",
};

// Initialize EmailJS when the script loads
(function () {
  if (typeof emailjs !== "undefined") {
    emailjs.init(EMAILJS_CONFIG.USER_ID);
    console.log("EmailJS initialized successfully");
  } else {
    console.error("EmailJS library not loaded");
  }
})();

// Export configuration for use in main.js
window.EMAILJS_CONFIG = EMAILJS_CONFIG;
