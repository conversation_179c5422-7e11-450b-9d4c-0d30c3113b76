// Portfolio Website JavaScript
// Author: S.Vivekrajiv
// Modern JavaScript with ES6+ features

class PortfolioWebsite {
  constructor() {
    this.init();
  }

  init() {
    this.setupEventListeners();
    this.setupSmoothScrolling();
    this.setupNavigation();
    this.setupFormHandling();
    this.setupAnimations();
    this.setupIntersectionObserver();
  }

  setupEventListeners() {
    // Wait for DOM to be fully loaded
    document.addEventListener("DOMContentLoaded", () => {
      console.log("Portfolio website loaded successfully");
    });

    // Handle window resize
    window.addEventListener(
      "resize",
      this.debounce(() => {
        this.handleResize();
      }, 250)
    );

    // Handle scroll events
    window.addEventListener(
      "scroll",
      this.throttle(() => {
        this.handleScroll();
      }, 16)
    ); // ~60fps
  }

  setupNavigation() {
    const navToggle = document.querySelector(".nav__toggle");
    const navMenu = document.querySelector(".nav__menu");
    const navLinks = document.querySelectorAll(".nav__link");

    // Mobile menu toggle
    if (navToggle && navMenu) {
      navToggle.addEventListener("click", () => {
        const isExpanded = navToggle.getAttribute("aria-expanded") === "true";

        navToggle.setAttribute("aria-expanded", !isExpanded);
        navToggle.classList.toggle("active");
        navMenu.classList.toggle("active");
      });
    }

    // Close mobile menu when clicking on links
    navLinks.forEach((link) => {
      link.addEventListener("click", () => {
        if (navMenu.classList.contains("active")) {
          navToggle.classList.remove("active");
          navMenu.classList.remove("active");
          navToggle.setAttribute("aria-expanded", "false");
        }
      });
    });

    // Close mobile menu when clicking outside
    document.addEventListener("click", (e) => {
      if (!e.target.closest(".nav") && navMenu.classList.contains("active")) {
        navToggle.classList.remove("active");
        navMenu.classList.remove("active");
        navToggle.setAttribute("aria-expanded", "false");
      }
    });
  }

  setupSmoothScrolling() {
    // Smooth scrolling for anchor links
    const anchorLinks = document.querySelectorAll('a[href^="#"]');

    anchorLinks.forEach((link) => {
      link.addEventListener("click", (e) => {
        e.preventDefault();

        const targetId = link.getAttribute("href");
        const targetElement = document.querySelector(targetId);

        if (targetElement) {
          const headerHeight = document.querySelector(".header").offsetHeight;
          const targetPosition = targetElement.offsetTop - headerHeight - 20;

          // Special handling for About section
          if (targetId === "#about") {
            // Reset animations first
            this.resetAboutAnimations();

            // Scroll to section
            window.scrollTo({
              top: targetPosition,
              behavior: "smooth",
            });

            // Trigger animations after scroll completes
            setTimeout(() => {
              this.animateAboutSection();
            }, 800); // Wait for smooth scroll to complete
          } else {
            window.scrollTo({
              top: targetPosition,
              behavior: "smooth",
            });
          }
        }
      });
    });
  }

  setupFormHandling() {
    const contactForm = document.querySelector(".contact__form");

    if (contactForm) {
      contactForm.addEventListener("submit", (e) => {
        e.preventDefault();
        this.handleFormSubmission(contactForm);
      });

      // Real-time form validation
      const formInputs = contactForm.querySelectorAll(
        ".form-input, .form-textarea"
      );
      formInputs.forEach((input) => {
        input.addEventListener("blur", () => {
          this.validateField(input);
        });

        input.addEventListener("input", () => {
          this.clearFieldError(input);
        });
      });
    }
  }

  handleFormSubmission(form) {
    const formData = new FormData(form);
    const data = Object.fromEntries(formData);

    // Basic validation
    if (this.validateForm(data)) {
      // Option 1: Use EmailJS (if configured)
      if (this.isEmailJSConfigured()) {
        this.sendEmailWithEmailJS(data, form);
      } else {
        // Option 2: Fallback to mailto link
        this.sendEmailWithMailto(data, form);
      }
    } else {
      this.showMessage(
        "Please fill in all required fields correctly.",
        "error"
      );
    }
  }

  isEmailJSConfigured() {
    // Check if EmailJS is loaded and configured
    return (
      typeof emailjs !== "undefined" &&
      window.emailjs &&
      window.EMAILJS_CONFIG &&
      window.EMAILJS_CONFIG.USER_ID !== "YOUR_USER_ID_HERE" &&
      window.EMAILJS_CONFIG.SERVICE_ID !== "YOUR_SERVICE_ID_HERE" &&
      window.EMAILJS_CONFIG.TEMPLATE_ID !== "YOUR_TEMPLATE_ID_HERE"
    );
  }

  sendEmailWithEmailJS(data, form) {
    // Show loading message
    this.showMessage("Sending your message...", "info");

    // Use configuration from emailjs-config.js
    const config = window.EMAILJS_CONFIG;

    emailjs
      .send(config.SERVICE_ID, config.TEMPLATE_ID, {
        from_name: data.name,
        from_email: data.email,
        subject: data.subject,
        message: data.message,
        to_email: "<EMAIL>",
        reply_to: data.email,
      })
      .then(() => {
        this.showMessage(
          "Thank you! Your message has been sent successfully. I'll get back to you soon.",
          "success"
        );
        form.reset();
      })
      .catch((error) => {
        console.error("EmailJS error:", error);
        this.showMessage(
          "Sorry, there was an error sending your message. Please try the email link below.",
          "error"
        );
        // Fallback to mailto
        this.sendEmailWithMailto(data, form);
      });
  }

  sendEmailWithMailto(data, form) {
    // Create email content
    const subject = encodeURIComponent(data.subject);
    const body = encodeURIComponent(
      `Name: ${data.name}\nEmail: ${data.email}\n\nMessage:\n${data.message}`
    );

    // Create mailto link
    const mailtoLink = `mailto:<EMAIL>?subject=${subject}&body=${body}`;

    // Open email client
    window.location.href = mailtoLink;

    // Show success message
    this.showMessage(
      "Opening your email client... Please send the email to complete your message.",
      "success"
    );

    // Reset form after a delay
    setTimeout(() => {
      form.reset();
    }, 2000);
  }

  validateForm(data) {
    const requiredFields = ["name", "email", "subject", "message"];
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;

    for (const field of requiredFields) {
      if (!data[field] || data[field].trim() === "") {
        return false;
      }
    }

    return emailRegex.test(data.email);
  }

  validateField(field) {
    const value = field.value.trim();
    const fieldName = field.name;

    if (!value) {
      this.showFieldError(field, "This field is required");
      return false;
    }

    if (fieldName === "email") {
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      if (!emailRegex.test(value)) {
        this.showFieldError(field, "Please enter a valid email address");
        return false;
      }
    }

    this.clearFieldError(field);
    return true;
  }

  showFieldError(field, message) {
    this.clearFieldError(field);

    const errorElement = document.createElement("span");
    errorElement.className = "field-error";
    errorElement.textContent = message;
    errorElement.style.color = "var(--color-error)";
    errorElement.style.fontSize = "var(--text-sm)";
    errorElement.style.marginTop = "var(--space-1)";
    errorElement.style.display = "block";

    field.parentNode.appendChild(errorElement);
    field.style.borderColor = "var(--color-error)";
  }

  clearFieldError(field) {
    const errorElement = field.parentNode.querySelector(".field-error");
    if (errorElement) {
      errorElement.remove();
    }
    field.style.borderColor = "";
  }

  showMessage(message, type = "info") {
    // Remove existing messages
    const existingMessage = document.querySelector(".message-popup");
    if (existingMessage) {
      existingMessage.remove();
    }

    const messageElement = document.createElement("div");
    messageElement.className = `message-popup message-popup--${type}`;
    messageElement.textContent = message;

    // Style the message
    Object.assign(messageElement.style, {
      position: "fixed",
      top: "20px",
      right: "20px",
      padding: "var(--space-4) var(--space-6)",
      borderRadius: "var(--radius-lg)",
      color: "white",
      fontWeight: "500",
      zIndex: "9999",
      transform: "translateX(100%)",
      transition: "transform var(--transition-normal)",
      backgroundColor:
        type === "success" ? "var(--color-success)" : "var(--color-error)",
    });

    document.body.appendChild(messageElement);

    // Animate in
    setTimeout(() => {
      messageElement.style.transform = "translateX(0)";
    }, 100);

    // Auto remove after 5 seconds
    setTimeout(() => {
      messageElement.style.transform = "translateX(100%)";
      setTimeout(() => {
        if (messageElement.parentNode) {
          messageElement.remove();
        }
      }, 300);
    }, 5000);
  }

  setupAnimations() {
    // Add loading animation to images
    const images = document.querySelectorAll("img");
    images.forEach((img) => {
      img.addEventListener("load", () => {
        img.style.opacity = "1";
      });

      if (img.complete) {
        img.style.opacity = "1";
      } else {
        img.style.opacity = "0";
        img.style.transition = "opacity var(--transition-normal)";
      }
    });
  }

  setupIntersectionObserver() {
    // Animate elements when they come into view
    const observerOptions = {
      threshold: 0.1,
      rootMargin: "0px 0px -100px 0px",
    };

    const observer = new IntersectionObserver((entries) => {
      entries.forEach((entry) => {
        if (entry.isIntersecting) {
          entry.target.classList.add("animate-in");

          // Special handling for About section
          if (entry.target.classList.contains("about__content")) {
            this.animateAboutSection();
          }
        }
      });
    }, observerOptions);

    // Observe elements that should animate on scroll
    const animateElements = document.querySelectorAll(
      ".section-header, .about__content, .contact__content"
    );

    animateElements.forEach((el) => {
      el.classList.add("animate-on-scroll");
      observer.observe(el);
    });

    // Add parallax effect to hero image
    this.setupParallaxEffect();
  }

  resetAboutAnimations() {
    // Reset all About section animations
    const aboutStory = document.querySelector(".about__story");
    const aboutHighlights = document.querySelector(".about__highlights");
    const storyTitle = document.querySelector(".about__story-title");
    const highlightsTitle = document.querySelector(".about__highlights-title");
    const timeline = document.querySelector(".timeline");
    const timelineItems = document.querySelectorAll(".timeline__item");
    const highlightItems = document.querySelectorAll(".highlights-list__item");

    // Remove animate-in class to reset animations
    aboutStory?.classList.remove("animate-in");
    aboutHighlights?.classList.remove("animate-in");
    storyTitle?.classList.remove("animate-in");
    highlightsTitle?.classList.remove("animate-in");
    timeline?.classList.remove("animate-in");

    timelineItems.forEach((item) => {
      item.classList.remove("animate-in");
    });

    highlightItems.forEach((item) => {
      item.classList.remove("animate-in");
    });
  }

  animateAboutSection() {
    // Animate About section elements
    const aboutStory = document.querySelector(".about__story");
    const aboutHighlights = document.querySelector(".about__highlights");
    const storyTitle = document.querySelector(".about__story-title");
    const highlightsTitle = document.querySelector(".about__highlights-title");
    const timeline = document.querySelector(".timeline");
    const timelineItems = document.querySelectorAll(".timeline__item");
    const highlightItems = document.querySelectorAll(".highlights-list__item");

    // Add animate-in class to trigger animations
    setTimeout(() => storyTitle?.classList.add("animate-in"), 100);
    setTimeout(() => aboutStory?.classList.add("animate-in"), 200);
    setTimeout(() => timeline?.classList.add("animate-in"), 300);
    setTimeout(() => highlightsTitle?.classList.add("animate-in"), 300);
    setTimeout(() => aboutHighlights?.classList.add("animate-in"), 400);

    // Animate timeline items
    timelineItems.forEach((item) => {
      setTimeout(() => item.classList.add("animate-in"), 500);
    });

    // Animate highlight items
    highlightItems.forEach((item) => {
      setTimeout(() => item.classList.add("animate-in"), 500);
    });
  }

  setupParallaxEffect() {
    const heroImage = document.querySelector(".hero__photo");
    if (heroImage) {
      window.addEventListener(
        "scroll",
        this.throttle(() => {
          const scrolled = window.pageYOffset;
          const rate = scrolled * -0.1;
          if (scrolled < window.innerHeight) {
            heroImage.style.transform = `translateY(${rate}px) scale(1)`;
          }
        }, 16)
      );
    }
  }

  handleScroll() {
    const header = document.querySelector(".header");
    const scrollY = window.scrollY;

    // Add/remove header background on scroll (keeping dark theme)
    if (scrollY > 100) {
      header.style.backgroundColor = "rgba(15, 23, 42, 0.98)";
      header.style.boxShadow = "var(--shadow-md)";
    } else {
      header.style.backgroundColor = "var(--color-gray-900)";
      header.style.boxShadow = "none";
    }

    // Update active navigation link
    this.updateActiveNavLink();
  }

  updateActiveNavLink() {
    const sections = document.querySelectorAll("section[id]");
    const navLinks = document.querySelectorAll(".nav__link");

    let currentSection = "";

    sections.forEach((section) => {
      const sectionTop = section.offsetTop - 100;
      const sectionHeight = section.offsetHeight;

      if (
        window.scrollY >= sectionTop &&
        window.scrollY < sectionTop + sectionHeight
      ) {
        currentSection = section.getAttribute("id");
      }
    });

    navLinks.forEach((link) => {
      link.classList.remove("active");
      if (link.getAttribute("href") === `#${currentSection}`) {
        link.classList.add("active");
      }
    });
  }

  handleResize() {
    // Handle any resize-specific logic
    const navMenu = document.querySelector(".nav__menu");
    const navToggle = document.querySelector(".nav__toggle");

    if (window.innerWidth > 768 && navMenu.classList.contains("active")) {
      navMenu.classList.remove("active");
      navToggle.classList.remove("active");
      navToggle.setAttribute("aria-expanded", "false");
    }
  }

  // Utility functions
  debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
      const later = () => {
        clearTimeout(timeout);
        func(...args);
      };
      clearTimeout(timeout);
      timeout = setTimeout(later, wait);
    };
  }

  throttle(func, limit) {
    let inThrottle;
    return function () {
      const args = arguments;
      const context = this;
      if (!inThrottle) {
        func.apply(context, args);
        inThrottle = true;
        setTimeout(() => (inThrottle = false), limit);
      }
    };
  }
}

// Initialize the portfolio website
new PortfolioWebsite();
