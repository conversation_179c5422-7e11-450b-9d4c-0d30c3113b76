/* CSS Reset for consistent cross-browser styling */

*,
*::before,
*::after {
    box-sizing: border-box;
    margin: 0;
    padding: 0;
}

html {
    font-size: 16px;
    scroll-behavior: smooth;
    -webkit-text-size-adjust: 100%;
    -ms-text-size-adjust: 100%;
}

body {
    line-height: 1.6;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    text-rendering: optimizeLegibility;
}

/* Remove default styling from common elements */
h1, h2, h3, h4, h5, h6 {
    font-weight: inherit;
    line-height: 1.2;
}

p, blockquote, pre, address, hr, table, fieldset, figure {
    margin-bottom: 0;
}

ul, ol {
    list-style: none;
}

a {
    text-decoration: none;
    color: inherit;
}

img, video, canvas, audio, iframe, embed, object {
    display: block;
    max-width: 100%;
    height: auto;
}

button, input, optgroup, select, textarea {
    font-family: inherit;
    font-size: inherit;
    line-height: inherit;
    margin: 0;
}

button {
    background: none;
    border: none;
    cursor: pointer;
}

table {
    border-collapse: collapse;
    border-spacing: 0;
}

/* Accessibility improvements */
:focus {
    outline: 2px solid #0066cc;
    outline-offset: 2px;
}

:focus:not(:focus-visible) {
    outline: none;
}

/* Reduce motion for users who prefer it */
@media (prefers-reduced-motion: reduce) {
    *,
    *::before,
    *::after {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
        scroll-behavior: auto !important;
    }
}

/* Print styles */
@media print {
    *,
    *::before,
    *::after {
        background: transparent !important;
        color: black !important;
        box-shadow: none !important;
        text-shadow: none !important;
    }
    
    a,
    a:visited {
        text-decoration: underline;
    }
    
    img {
        page-break-inside: avoid;
    }
    
    p,
    h2,
    h3 {
        orphans: 3;
        widows: 3;
    }
    
    h2,
    h3 {
        page-break-after: avoid;
    }
}
