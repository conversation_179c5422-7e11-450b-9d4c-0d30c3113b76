<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Upload Your Profile Image</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .upload-area {
            border: 2px dashed #ccc;
            border-radius: 10px;
            padding: 40px;
            text-align: center;
            margin: 20px 0;
            transition: border-color 0.3s;
        }
        .upload-area:hover {
            border-color: #007bff;
        }
        .upload-area.dragover {
            border-color: #007bff;
            background-color: #f0f8ff;
        }
        input[type="file"] {
            display: none;
        }
        .upload-btn {
            background-color: #007bff;
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px;
        }
        .upload-btn:hover {
            background-color: #0056b3;
        }
        .preview {
            margin: 20px 0;
            text-align: center;
        }
        .preview img {
            max-width: 300px;
            max-height: 300px;
            border-radius: 50%;
            border: 4px solid #fff;
            box-shadow: 0 4px 8px rgba(0,0,0,0.2);
        }
        .instructions {
            background-color: #e7f3ff;
            padding: 20px;
            border-radius: 5px;
            margin: 20px 0;
        }
        .instructions h3 {
            color: #0066cc;
            margin-top: 0;
        }
        .step {
            margin: 10px 0;
            padding: 10px;
            background-color: #f8f9fa;
            border-left: 4px solid #007bff;
        }
        .download-btn {
            background-color: #28a745;
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px;
            display: none;
        }
        .download-btn:hover {
            background-color: #218838;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>📸 Upload Your Profile Image</h1>
        
        <div class="instructions">
            <h3>Instructions:</h3>
            <div class="step">
                <strong>Step 1:</strong> Choose a professional photo (headshot or portrait)
            </div>
            <div class="step">
                <strong>Step 2:</strong> Upload your image using the area below
            </div>
            <div class="step">
                <strong>Step 3:</strong> Download the processed image
            </div>
            <div class="step">
                <strong>Step 4:</strong> Save it as "profile.jpg" in the "images" folder of your portfolio
            </div>
        </div>

        <div class="upload-area" id="uploadArea">
            <p>📁 Drag and drop your image here or click to browse</p>
            <button class="upload-btn" onclick="document.getElementById('fileInput').click()">
                Choose Image
            </button>
            <input type="file" id="fileInput" accept="image/*">
        </div>

        <div class="preview" id="preview"></div>
        
        <div style="text-align: center;">
            <button class="download-btn" id="downloadBtn">Download Processed Image</button>
        </div>

        <div class="instructions">
            <h3>📋 Manual Upload Instructions:</h3>
            <p><strong>If you prefer to manually add your image:</strong></p>
            <ol>
                <li>Save your professional photo as <code>profile.jpg</code></li>
                <li>Copy it to the <code>images</code> folder in your portfolio directory</li>
                <li>The path should be: <code>Portfolio/images/profile.jpg</code></li>
                <li>Refresh your portfolio website to see your image</li>
            </ol>
        </div>
    </div>

    <script>
        const uploadArea = document.getElementById('uploadArea');
        const fileInput = document.getElementById('fileInput');
        const preview = document.getElementById('preview');
        const downloadBtn = document.getElementById('downloadBtn');
        let processedCanvas = null;

        // Handle drag and drop
        uploadArea.addEventListener('dragover', (e) => {
            e.preventDefault();
            uploadArea.classList.add('dragover');
        });

        uploadArea.addEventListener('dragleave', () => {
            uploadArea.classList.remove('dragover');
        });

        uploadArea.addEventListener('drop', (e) => {
            e.preventDefault();
            uploadArea.classList.remove('dragover');
            const files = e.dataTransfer.files;
            if (files.length > 0) {
                handleFile(files[0]);
            }
        });

        // Handle file input
        fileInput.addEventListener('change', (e) => {
            if (e.target.files.length > 0) {
                handleFile(e.target.files[0]);
            }
        });

        function handleFile(file) {
            if (!file.type.startsWith('image/')) {
                alert('Please select an image file.');
                return;
            }

            const reader = new FileReader();
            reader.onload = (e) => {
                processImage(e.target.result);
            };
            reader.readAsDataURL(file);
        }

        function processImage(imageSrc) {
            const img = new Image();
            img.onload = () => {
                // Create canvas for processing
                const canvas = document.createElement('canvas');
                const ctx = canvas.getContext('2d');
                
                // Set canvas size (square format)
                const size = 400;
                canvas.width = size;
                canvas.height = size;
                
                // Calculate crop dimensions to center the image
                const scale = Math.max(size / img.width, size / img.height);
                const scaledWidth = img.width * scale;
                const scaledHeight = img.height * scale;
                const x = (size - scaledWidth) / 2;
                const y = (size - scaledHeight) / 2;
                
                // Draw image
                ctx.drawImage(img, x, y, scaledWidth, scaledHeight);
                
                // Show preview
                const previewImg = document.createElement('img');
                previewImg.src = canvas.toDataURL('image/jpeg', 0.9);
                previewImg.style.maxWidth = '300px';
                previewImg.style.maxHeight = '300px';
                previewImg.style.borderRadius = '50%';
                previewImg.style.border = '4px solid #fff';
                previewImg.style.boxShadow = '0 4px 8px rgba(0,0,0,0.2)';
                
                preview.innerHTML = '<h3>Preview:</h3>';
                preview.appendChild(previewImg);
                
                // Store processed canvas
                processedCanvas = canvas;
                downloadBtn.style.display = 'inline-block';
            };
            img.src = imageSrc;
        }

        // Download processed image
        downloadBtn.addEventListener('click', () => {
            if (processedCanvas) {
                const link = document.createElement('a');
                link.download = 'profile.jpg';
                link.href = processedCanvas.toDataURL('image/jpeg', 0.9);
                link.click();
            }
        });
    </script>
</body>
</html>
